# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Flightlogger2 is an Android application for flight data logging, specifically designed to capture altitude data from USB-connected sensors and GPS location data. The app creates CSV flight logs combining sensor readings with GPS coordinates for flight analysis.

## Build System

This is an Android project using Gradle with Kotlin DSL:

- **Build**: `./gradlew build`
- **Debug Build**: `./gradlew assembleDebug`
- **Release Build**: `./gradlew assembleRelease`
- **Clean**: `./gradlew clean`
- **Install Debug**: `./gradlew installDebug`

## Architecture

### Core Components

- **MainActivity.java**: Single activity hosting fragments, handles USB device attachment intents
- **DevicesFragment.java**: Lists and manages USB serial devices, handles device selection and configuration (baud rate, read mode)
- **TerminalFragment.java**: Main logging interface that:
  - Connects to USB serial devices for altitude data
  - Tracks GPS location and speed
  - Creates timestamped CSV logs in Downloads folder
  - Displays real-time status (GPS, Battery, Power, Altitude)

### Key Features

- **USB Serial Communication**: Uses `usb-serial-for-android` library for device communication
- **GPS Logging**: Tracks location, speed, and creates flight logs
- **Data Logging**: Saves CSV files with format: `timestamp,latitude,longitude,altitude`
- **Real-time Display**: Shows GPS status, ground speed, AGL altitude, battery, and power status

### Dependencies

Key libraries used:
- `com.github.mik3y:usb-serial-for-android:3.8.1` - USB serial communication
- `androidx.appcompat` - Android support components
- `com.google.android.material` - Material Design components
- `com.google.android.gms:play-services-location` - Location services

### File Structure

- `app/src/main/java/com/drakkentech/flightlogger2/` - Main source code
- `app/src/main/res/` - Android resources (layouts, menus, strings)
- `app/build.gradle.kts` - App-level build configuration
- `build.gradle.kts` - Project-level build configuration

### Data Flow

1. DevicesFragment detects and lists USB devices
2. User selects device → launches TerminalFragment
3. TerminalFragment connects to USB device and starts GPS tracking
4. Altitude data received via USB serial → parsed and displayed
5. GPS location updates → combined with altitude in CSV log
6. Log files saved to Downloads folder with timestamp

### Testing

- Unit tests: `./gradlew test`
- Instrumentation tests: `./gradlew connectedAndroidTest`
- Tests located in `app/src/test/` and `app/src/androidTest/`

## Development Notes

- Target SDK: 34, Min SDK: 27
- Java 11 compatibility
- View binding enabled
- Landscape orientation locked for main activity
- Location and storage permissions required