<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Logger 2 - Log Laser Distance and GPS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .beta-badge {
            display: inline-block;
            background: #ff6b35;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .description {
            font-size: 1.1rem;
            margin-bottom: 30px;
            color: #555;
        }
        
        .specs {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .specs h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .specs ul {
            list-style: none;
        }
        
        .specs li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }
        
        .specs li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4facfe;
            font-weight: bold;
        }
        
        .specs li:last-child {
            border-bottom: none;
        }
        
        .download-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }
        
        .license-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .license-info h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .license-info p {
            color: #856404;
            margin: 0;
        }
        
        .thanks {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        
        .thanks p {
            color: #0c5460;
            margin: 10px 0;
        }
        
        .lightware-logo {
            max-width: 100%;
            height: auto;
            margin-top: 15px;
            border-radius: 8px;
        }

        .drakkentech-footer {
            text-align: center;
            padding: 30px 0;
            border-top: 1px solid #eee;
            margin-top: 30px;
        }

        .drakkentech-logo {
            max-width: 200px;
            height: auto;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .drakkentech-logo:hover {
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Flight Logger 2</h1>
            <p>Log Laser Distance and GPS</p>
        </div>
        
        <div class="content">
            <div class="beta-badge">Beta Release</div>
            
            <div class="description">
                This is a 'minimum viable product' beta for the Flightlogger app for Android and the 
                <a href="https://lightwarelidar.com/shop/sf30-d-200-m/" target="_blank" style="color: #4facfe; text-decoration: none;">Lightware SF30/D laser rangefinder</a>.
            </div>
            
            <div class="specs">
                <h3>Compatibility</h3>
                <ul>
                    <li>Android 10+ required</li>
                    <li>Any Lightware SF30/D via USB connection</li>
                    <li>USB OTG adapter may be needed</li>
                </ul>
            </div>
            
            <div class="download-section">
                <a href="flightlogger2_0.1.apk" download class="download-btn">
                    📱 Download APK
                </a>
            </div>
            
            <div class="license-info">
                <h4>⚠️ Important Notice</h4>
                <p>Released under MIT license - no guarantees as to performance! This is beta software, use at your own risk.</p>
            </div>
            
            <div class="thanks">
                <p><strong>Special Thanks</strong></p>
                <p>Thanks to Lightware for providing a rangefinder for testing!</p>
                <img src="Lightware_logo.png" alt="Lightware Logo" class="lightware-logo">
            </div>

            <div class="drakkentech-footer">
                <img src="drakkentech_logo_wide1.png" alt="Drakkentech Logo" class="drakkentech-logo">
            </div>
        </div>
    </div>
</body>
</html>
