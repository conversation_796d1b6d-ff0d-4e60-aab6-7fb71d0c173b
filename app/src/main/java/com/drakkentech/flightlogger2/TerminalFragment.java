package com.drakkentech.flightlogger2;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.method.ScrollingMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.hoho.android.usbserial.driver.UsbSerialDriver;
import com.hoho.android.usbserial.driver.UsbSerialPort;
import com.hoho.android.usbserial.driver.UsbSerialProber;
import com.hoho.android.usbserial.util.HexDump;
import com.hoho.android.usbserial.util.SerialInputOutputManager;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.EnumSet;

import android.location.Location;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;

import android.location.LocationManager;
import android.location.LocationListener;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.BatteryManager;

import androidx.core.app.ActivityCompat;

import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;

// log gps
import android.Manifest;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Environment;
import android.widget.Toast;
import androidx.core.app.ActivityCompat;
import android.util.Log;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Locale;
import androidx.annotation.RequiresApi;


public class TerminalFragment extends Fragment implements SerialInputOutputManager.Listener {

    //log gps
    private static final String TAG = "GPSLogger";
    private static final String FILE_NAME = "gps_log.txt";
    private int count = 0;
    private ContentValues values = new ContentValues();

    private String logFileName;

    private File logFile;

    private TextView gpsStatus, groundSpeed, altStatus, batStatus, pwrStatus;
    private Button refreshGpsButton;
    private LocationManager locationManager;
    private Location lastLocation;
    private LocationListener locationListener;

    private double currentLatitude = Double.NaN;
    private double currentLongitude = Double.NaN;


    private static final int LOCATION_PERMISSION_REQUEST_CODE = 100;
    private boolean isUsbConnected = false;

    private enum UsbPermission { Unknown, Requested, Granted, Denied }
    //    com.drakkentech.myapplicationtest
//    private static final String INTENT_ACTION_GRANT_USB = BuildConfig.APPLICATION_ID + ".GRANT_USB";
    private static final String INTENT_ACTION_GRANT_USB = com.drakkentech.flightlogger2.BuildConfig.APPLICATION_ID + ".GRANT_USB";

    private static final int WRITE_WAIT_MILLIS = 2000;
    private static final int READ_WAIT_MILLIS = 2000;

    private int portNum, baudRate;
    private boolean withIoManager;

    private final BroadcastReceiver broadcastReceiver;
    private final Handler mainLooper;
    private TextView receiveText, statusText;
    private ControlLines controlLines;

    private SerialInputOutputManager usbIoManager;
    private UsbSerialPort usbSerialPort;
    private UsbPermission usbPermission = UsbPermission.Unknown;
    private boolean connected = false;

    private long lastSaveTimeGPS = 0;
    private long lastSaveTimeAGL = 0;

    public TerminalFragment() {
        broadcastReceiver = new BroadcastReceiver() {
            //            @Override
//            public void onReceive(Context context, Intent intent) {
//                if(INTENT_ACTION_GRANT_USB.equals(intent.getAction())) {
//                    usbPermission = intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)
//                            ? UsbPermission.Granted : UsbPermission.Denied;
//                    connect();
//                }
//            }
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                    Log.d("USB", "Device Attached: Attempting to reconnect...");
                    usbPermission = UsbPermission.Unknown;
                    mainLooper.postDelayed(() -> connect(), 1000); // 1-second delay before reconnecting
                } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                    Log.d("USB", "Device Detached: Disconnecting...");
                    disconnect();
                } else if (INTENT_ACTION_GRANT_USB.equals(action)) {
                    usbPermission = intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)
                            ? UsbPermission.Granted : UsbPermission.Denied;
                    connect();
                }
            }
        };
        mainLooper = new Handler(Looper.getMainLooper());
    }

    private final BroadcastReceiver batteryReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
            boolean isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                    status == BatteryManager.BATTERY_STATUS_FULL;
            Log.e("battery", isCharging + " TextView is null!");
            if (isCharging) {
                checkGPSStatus();
                checkBatteryStatus();
                checkPowerStatus();
            } else {
                checkGPSStatus();
                checkBatteryStatus();
                checkPowerStatus();
            }
        }
    };

    private final BroadcastReceiver powerReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.e("battery", action + " TextView is null!");
            if (Intent.ACTION_POWER_CONNECTED.equals(action)) {
                checkGPSStatus();
                checkBatteryStatus();
                checkPowerStatus();
            } else if (Intent.ACTION_POWER_DISCONNECTED.equals(action)) {
                checkGPSStatus();
                checkBatteryStatus();
                checkPowerStatus();
            }
        }
    };

    /*
     * Lifecycle
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
        setRetainInstance(true);
        portNum = getArguments().getInt("port");
        baudRate = getArguments().getInt("baud");
        withIoManager = getArguments().getBoolean("withIoManager");
    }

//    @Override
//    public void onStart() {
//        super.onStart();
//        ContextCompat.registerReceiver(getActivity(), broadcastReceiver, new IntentFilter(INTENT_ACTION_GRANT_USB), ContextCompat.RECEIVER_NOT_EXPORTED);
//    }

    @Override
    public void onStart() {
        super.onStart();
        IntentFilter filter = new IntentFilter();
        filter.addAction(INTENT_ACTION_GRANT_USB);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        ContextCompat.registerReceiver(getActivity(), broadcastReceiver, filter, ContextCompat.RECEIVER_NOT_EXPORTED);
    }

    @Override
    public void onStop() {
        getActivity().unregisterReceiver(broadcastReceiver);
        super.onStop();
    }

    @Override
    public void onResume() {
        super.onResume();
        if(!connected && (usbPermission == UsbPermission.Unknown || usbPermission == UsbPermission.Granted))
            mainLooper.post(this::connect);
        // Battery
        requireContext().registerReceiver(batteryReceiver, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
        // PowerReceiver
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_POWER_CONNECTED);
        filter.addAction(Intent.ACTION_POWER_DISCONNECTED);
        requireContext().registerReceiver(powerReceiver, filter);
    }

    @Override
    public void onPause() {
        if(connected) {
            status("disconnected");
            disconnect();
        }
        if (locationManager != null && locationListener != null) {
            locationManager.removeUpdates(locationListener);
        }
        super.onPause();
        // battery
        requireContext().unregisterReceiver(batteryReceiver);
        // power
        requireContext().unregisterReceiver(powerReceiver);
    }
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_terminal, container, false);

        receiveText = view.findViewById(R.id.receive_text);
        receiveText.setTextColor(getResources().getColor(R.color.colorRecieveText));
        receiveText.setMovementMethod(ScrollingMovementMethod.getInstance());
        statusText = view.findViewById(R.id.status_text);
        statusText.setTextColor(getResources().getColor(R.color.colorStatusText));
        View receiveBtn = view.findViewById(R.id.receive_btn);
        controlLines = new ControlLines(view);
        if(withIoManager) {
            receiveBtn.setVisibility(View.GONE);
        } else {
            receiveBtn.setOnClickListener(v -> read());
        }

        gpsStatus = view.findViewById(R.id.gpsStatus);
        groundSpeed = view.findViewById(R.id.groundSpeed);
        altStatus = view.findViewById(R.id.altStatus);
        batStatus = view.findViewById(R.id.batStatus);
        pwrStatus = view.findViewById(R.id.pwrStatus);
        refreshGpsButton = view.findViewById(R.id.refreshGpsButton);
        locationManager = (LocationManager) getActivity().getSystemService(Context.LOCATION_SERVICE);

        checkGPSStatus();
        checkBatteryStatus();
        checkPowerStatus();

        refreshGpsButton.setOnClickListener(v -> {
            checkGPSStatus();
            checkBatteryStatus();
            checkPowerStatus();
            controlLines = new ControlLines(view);
            if(withIoManager) {
                receiveBtn.setVisibility(View.GONE);
            } else {
                receiveBtn.setOnClickListener(b -> read());
            }
        });

        return view;

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startTrackingLocation();
            } else {
                Toast.makeText(getContext(), "Location permissions not granted", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void checkGPSStatus() {
        if (locationManager == null) {
            return;
        }
//        if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
//            updateTextView(gpsStatus, "GPS Inactive", android.R.color.holo_red_dark);
//        } else {
//            updateTextView(gpsStatus, "GPS Active", android.R.color.holo_green_dark);
//            startTrackingLocation();
//        }
        if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            updateTextView(gpsStatus, "GPS", android.R.color.holo_red_dark);
        } else {
            updateTextView(gpsStatus, "GPS", android.R.color.holo_green_dark);
            startTrackingLocation();
        }
    }

    private void checkBatteryStatus() {
        BatteryManager batteryManager = (BatteryManager) getActivity().getSystemService(Context.BATTERY_SERVICE);
        int batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);

//        if (batteryLevel > 50) {
//            updateTextView(batStatus, "BAT OK (" + batteryLevel + "%)", android.R.color.holo_green_dark);
//        } else {
//            updateTextView(batStatus, "BAT Low (" + batteryLevel + "%)", android.R.color.holo_red_dark);
//        }
        if (batteryLevel > 50) {
            updateTextView(batStatus, "BAT", android.R.color.holo_green_dark);
        } else {
            updateTextView(batStatus, "BAT", android.R.color.holo_red_dark);
        }
    }

    private void checkPowerStatus() {
        UsbManager usbManager = (UsbManager) getActivity().getSystemService(Context.USB_SERVICE);
        isUsbConnected = false;
        for (UsbDevice device : usbManager.getDeviceList().values()) {
            isUsbConnected = true;
            break;
        }

//        if (isUsbConnected) {
//            updateTextView(pwrStatus, "PWR Connected", android.R.color.holo_green_dark);
//            updateTextView(altStatus, "ALT Connected", android.R.color.holo_green_dark);
//        } else {
//            updateTextView(pwrStatus, "PWR Not Connected", android.R.color.holo_red_dark);
//            updateTextView(altStatus, "ALT Not Connected", android.R.color.holo_red_dark);
//        }
        if (isUsbConnected) {
            updateTextView(pwrStatus, "PWR", android.R.color.holo_green_dark);
            updateTextView(altStatus, "ALT", android.R.color.holo_green_dark);
        } else {
            updateTextView(pwrStatus, "PWR", android.R.color.holo_red_dark);
            updateTextView(altStatus, "ALT", android.R.color.holo_red_dark);
        }
    }
    private void updateTextView(TextView textView, String text, int color) {
        if (textView != null) {
            textView.setText(text);
            textView.setTextColor(getResources().getColor(color));
        } else {
            Log.e("TerminalFragment", text + " TextView is null!");
        }
    }
//    private void startTrackingLocation() {
//        try {
//            if (ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
//                requestPermissions(new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, LOCATION_PERMISSION_REQUEST_CODE);
//                return;
//            }
//
//            locationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 1000, 5, new LocationListener() {
//                @Override
//                public void onLocationChanged(Location location) {
//                    if (lastLocation != null) {
//                        float timeDifferenceInSeconds = (location.getTime() - lastLocation.getTime()) / 1000.0f;
//                        float speedInMetersPerSecond = location.distanceTo(lastLocation) / timeDifferenceInSeconds;
//                        float speedInKnots = speedInMetersPerSecond * 1.943844f;
//
//                        Log.d("GroundSpeed", "Speed in m/s: " + speedInMetersPerSecond);
//                        Log.d("GroundSpeed", "Speed in knots: " + speedInKnots);
//
//                        updateTextView(groundSpeed, String.format("%.2f Knots", speedInKnots), android.R.color.white);
//                    }
//                    lastLocation = location;
//                }
//
//                @Override
//                public void onStatusChanged(String provider, int status, Bundle extras) {}
//
//                @Override
//                public void onProviderEnabled(String provider) {}
//
//                @Override
//                public void onProviderDisabled(String provider) {}
//            });
//        } catch (SecurityException e) {
//            Toast.makeText(getContext(), "Location permissions not granted", Toast.LENGTH_SHORT).show();
//        }
//    }

    private void startTrackingLocation() {
        try {
            if (ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, LOCATION_PERMISSION_REQUEST_CODE);
                return;

            }
            if (locationManager == null) {
                return;
            }
            initializeLogFile();
            locationListener = new LocationListener() {
                @Override
                public void onLocationChanged(Location location) {
                    currentLatitude = location.getLatitude();
                    currentLongitude = location.getLongitude();
                    if (lastLocation != null) {
                        float timeDifferenceInSeconds = (location.getTime() - lastLocation.getTime()) / 1000.0f;

                        // Prevent division by zero and ensure valid updates
                        if (timeDifferenceInSeconds > 0) {
                            float speedInMetersPerSecond = location.distanceTo(lastLocation) / timeDifferenceInSeconds;
                            float speedInKnots = speedInMetersPerSecond * 1.943844f;

                            // Ensure valid speed values
                            if (!Float.isNaN(speedInKnots) && !Float.isInfinite(speedInKnots)) {
                                Log.d("GroundSpeed", "Speed in m/s: " + speedInMetersPerSecond);
                                Log.d("GroundSpeed", "Speed in knots: " + speedInKnots);

                                // Update TextView
                                updateTextView(groundSpeed, String.format("%.2f Knots", speedInKnots), android.R.color.white);
                                // Only save every 1 second
                                long currentTime = System.currentTimeMillis();
                                if (currentTime - lastSaveTimeGPS >= 1000) {
                                    lastSaveTimeGPS = currentTime; // Update last save timestamp
                                }
                            } else {
                                Log.w("GroundSpeed", "Invalid speed detected (NaN or Infinity), skipping update.");
                            }
                        } else {
                            Log.w("GroundSpeed", "Time difference too small, skipping update.");
                        }
                    }
                    lastLocation = location;
                }

                @Override
                public void onStatusChanged(String provider, int status, Bundle extras) {}

                @Override
                public void onProviderEnabled(String provider) {}

                @Override
                public void onProviderDisabled(String provider) {}
            };
            locationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 1000, 5, locationListener);
        } catch (SecurityException e) {
            Toast.makeText(getContext(), "Location permissions not granted", Toast.LENGTH_SHORT).show();
        }
    }

//    private void initializeLogFile() {
//        // Create a filename with a timestamp (e.g., ground_speed_log_2025-03-18_12-30-00.txt)
//        String timestamp = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault()).format(new Date());
//        logFileName = "ground_speed_log_" + timestamp + ".txt";
//    }

    private void initializeLogFile() {
        if (logFileName == null || logFileName.isEmpty()) {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            logFileName = "flightlog_" + timestamp + ".csv";
            String header = "timestamp,latitude,longitude,altitude";
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                saveToDownloadsUsingMediaStore(header);
            } else {
                saveToDownloadsUsingLegacyAPI(header);
            }
        }
    }

    private void appendLogEntry(double altitudeFeet) {
        if (logFileName == null) {
            initializeLogFile();
        }
        String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date());
        String latStr = Double.isNaN(currentLatitude) ? "NA" : String.valueOf(currentLatitude);
        String lonStr = Double.isNaN(currentLongitude) ? "NA" : String.valueOf(currentLongitude);
        String line = String.format(Locale.US, "%s,%s,%s,%.0f", timestamp, latStr, lonStr, altitudeFeet);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            saveToDownloadsUsingMediaStore(line);
        } else {
            saveToDownloadsUsingLegacyAPI(line);
        }
    }
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private void saveToDownloadsUsingMediaStore(String data) {
        ContentResolver contentResolver = getContext().getContentResolver();

        // Check if the file already exists
        Uri existingFileUri = null;
        Uri collection = MediaStore.Downloads.getContentUri(MediaStore.VOLUME_EXTERNAL);
        String selection = MediaStore.MediaColumns.DISPLAY_NAME + " = ?";
        String[] selectionArgs = new String[]{logFileName};

        try (Cursor cursor = contentResolver.query(collection, new String[]{MediaStore.MediaColumns._ID}, selection, selectionArgs, null)) {
            if (cursor != null && cursor.moveToFirst()) {
                long id = cursor.getLong(0);
                existingFileUri = ContentUris.withAppendedId(collection, id);
            }
        }

        Uri fileUri;
        if (existingFileUri != null) {
            // File already exists, use existing URI
            fileUri = existingFileUri;
        } else {
            // File does not exist, create a new one
            ContentValues values = new ContentValues();
            values.put(MediaStore.MediaColumns.DISPLAY_NAME, logFileName);
            values.put(MediaStore.MediaColumns.MIME_TYPE, "text/csv");
            values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS);

            fileUri = contentResolver.insert(collection, values);
        }

        if (fileUri != null) {
            try (OutputStream fos = contentResolver.openOutputStream(fileUri, "wa")) { // "wa" means append
                if (fos != null) {
                    fos.write((data + "\n").getBytes());
                    Log.d("FlightLog", "Data appended to file: " + logFileName);
                }
            } catch (IOException e) {
                Log.e("FlightLog", "Error writing to file", e);
            }
        }
    }

//    // For Android 10 (API 29) and above using MediaStore
//    private void saveToDownloadsUsingMediaStore(String speedData) {
//        ContentValues values = new ContentValues();
//            values.put(MediaStore.MediaColumns.DISPLAY_NAME, logFileName); // Use the timestamped filename
//            values.put(MediaStore.MediaColumns.MIME_TYPE, "text/plain");
//            values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS); // Save to Downloads
//
//
//        Uri uri = getContext().getContentResolver().insert(MediaStore.Files.getContentUri("external"), values);
//
//        try (OutputStream fos = getContext().getContentResolver().openOutputStream(uri)) {
//            if (fos != null) {
//                fos.write((speedData + "\n").getBytes());
//                Log.d("GroundSpeed", "Speed data saved to file: " + speedData);
//            }
//        } catch (IOException e) {
//            Log.e("GroundSpeed", "Error writing to file", e);
//        }
//    }

    // For Android 9 (API 28) and below using Environment API
    private void saveToDownloadsUsingLegacyAPI(String data) {
        if (ContextCompat.checkSelfPermission(getContext(), Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            // Request permission if not granted
            ActivityCompat.requestPermissions(getActivity(),
                    new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, 1);
            return;
        }

        // Use the public Downloads directory with the timestamped filename
        File file = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), logFileName);
        try {
            // Ensure the file exists, create it if it doesn't
            if (!file.exists()) {
                file.createNewFile();
            }

            // Open the file in append mode
            FileOutputStream fos = new FileOutputStream(file, true);  // 'true' means append to file
            fos.write((data + "\n").getBytes());
            fos.close();

            Log.d("FlightLog", "Data saved to file: " + data);
        } catch (IOException e) {
            Log.e("FlightLog", "Error writing to file", e);
        }
    }


    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.menu_terminal, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.clear) {
            receiveText.setText("");
            return true;
        } else if( id == R.id.send_break) {
            if(!connected) {
                Toast.makeText(getActivity(), "not connected", Toast.LENGTH_SHORT).show();
            } else {
                try {
                    usbSerialPort.setBreak(true);
                    Thread.sleep(100); // should show progress bar instead of blocking UI thread
                    usbSerialPort.setBreak(false);
                    SpannableStringBuilder spn = new SpannableStringBuilder();
                    spn.append("send <break>\n");
                    spn.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.colorSendText)), 0, spn.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    receiveText.append(spn);
                } catch(UnsupportedOperationException ignored) {
                    Toast.makeText(getActivity(), "BREAK not supported", Toast.LENGTH_SHORT).show();
                } catch(Exception e) {
                    Toast.makeText(getActivity(), "BREAK failed: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            }
            return true;
        } else {
            return super.onOptionsItemSelected(item);
        }
    }

    /*
     * Serial
     */
    @Override
    public void onNewData(byte[] data) {
        mainLooper.post(() -> {
            receive(data);
        });
    }

    @Override
    public void onRunError(Exception e) {
        Log.e("SerialError", "Connection lost: " + e.getMessage());
    }

    /*
     * Serial + UI
     */
    private void connect() {
        UsbDevice device = null;
        UsbManager usbManager = (UsbManager) getActivity().getSystemService(Context.USB_SERVICE);
        checkGPSStatus();
        checkBatteryStatus();
        checkPowerStatus();
        for (UsbDevice v : usbManager.getDeviceList().values()) {
            device = v;
            break;
        }
        if (device == null) {
            status("connection failed: device not found");
            return;
        }
        UsbSerialDriver driver = UsbSerialProber.getDefaultProber().probeDevice(device);
        if(driver == null) {
            driver = CustomProber.getCustomProber().probeDevice(device);
        }
        if(driver == null) {
            status("connection failed: no driver for device");
            return;
        }
        if(driver.getPorts().size() < portNum) {
            status("connection failed: not enough ports at device");
            return;
        }
        usbSerialPort = driver.getPorts().get(portNum);
        UsbDeviceConnection usbConnection = usbManager.openDevice(driver.getDevice());
        if(usbConnection == null && usbPermission == UsbPermission.Unknown && !usbManager.hasPermission(driver.getDevice())) {
            usbPermission = UsbPermission.Requested;
            int flags = Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_MUTABLE : 0;
            Intent intent = new Intent(INTENT_ACTION_GRANT_USB);
            intent.setPackage(getActivity().getPackageName());
            PendingIntent usbPermissionIntent = PendingIntent.getBroadcast(getActivity(), 0, intent, flags);
            usbManager.requestPermission(driver.getDevice(), usbPermissionIntent);
            return;
        }
        if(usbConnection == null) {
            if (!usbManager.hasPermission(driver.getDevice()))
                status("connection failed: permission denied");
            else
                status("connection failed: open failed");
            return;
        }

        try {
            usbSerialPort.open(usbConnection);
            try{
                usbSerialPort.setParameters(baudRate, 8, 1, UsbSerialPort.PARITY_NONE);
            }catch (UnsupportedOperationException e){
                status("unsupport setparameters");
            }
            if(withIoManager) {
                usbIoManager = new SerialInputOutputManager(usbSerialPort, this);
                usbIoManager.start();
            }
            status("connected");
            connected = true;
            controlLines.start();
        } catch (Exception e) {
            status("connection failed: " + e.getMessage());
            disconnect();
        }
    }

    private void disconnect() {
        if (usbIoManager != null) {
            usbIoManager.stop();
            usbIoManager = null;
        }
        try {
            if (usbSerialPort != null) {
                usbSerialPort.close();
                usbSerialPort = null;
            }
        } catch (IOException ignored) { }
        usbPermission = UsbPermission.Unknown;
        connected = false;
        status("disconnected");
    }

//    private void disconnect() {
//        connected = false;
//        controlLines.stop();
////        if (controlLines != null) {
////            controlLines.stop(); // Call stop() only if controlLines is initialized
////        } else {
////            Log.e("TerminalFragment", "ControlLines object is not initialized!");
////        }
//        if(usbIoManager != null) {
//            usbIoManager.setListener(null);
//            usbIoManager.stop();
//        }
//        usbIoManager = null;
//        try {
//            usbSerialPort.close();
//        } catch (IOException ignored) {}
//        usbSerialPort = null;
//    }

    private void send(String str) {
        if(!connected) {
            Toast.makeText(getActivity(), "not connected", Toast.LENGTH_SHORT).show();
            return;
        }
        try {
            byte[] data = (str + '\n').getBytes();
            SpannableStringBuilder spn = new SpannableStringBuilder();
            spn.append("send " + data.length + " bytes\n");
            spn.append(HexDump.dumpHexString(data)).append("\n");
            spn.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.colorSendText)), 0, spn.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            receiveText.append(spn);
            usbSerialPort.write(data, WRITE_WAIT_MILLIS);
        } catch (Exception e) {
            onRunError(e);
        }
    }

    private void read() {
        if(!connected) {
            Toast.makeText(getActivity(), "not connected", Toast.LENGTH_SHORT).show();
            return;
        }
        try {
            byte[] buffer = new byte[8192];
            int len = usbSerialPort.read(buffer, READ_WAIT_MILLIS);
            if (len <= 0) {
                Log.w("TerminalFragment", "read returned " + len);
                return;
            }
            receive(Arrays.copyOf(buffer, len));
        } catch (IOException e) {
            // when using read with timeout, USB bulkTransfer returns -1 on timeout _and_ errors
            // like connection loss, so there is typically no exception thrown here on error
            status("connection lost: " + e.getMessage());
            disconnect();
        }
    }

    private void receive(byte[] data) {
        if (data.length > 0) {
            String decodedString = new String(data).trim(); // Convert bytes to string
            decodedString = decodedString.replaceAll("[^0-9.]", ""); // Remove non-numeric characters

            if (!decodedString.isEmpty()) {
                try {
                    double meters = Double.parseDouble(decodedString);
                    double feet = meters * 3.28084; // Convert meters to feet

                    // Display only the latest value
//                    String displayText = String.format("AGL\n%.2f\nFeet", feet);
                    String displayText = String.format("%.2f AGL", feet);
                    receiveText.setText(displayText); // Use setText() instead of append()
//                    saveSpeedToFile(String.format("%.2f AGL", feet));
                    // Only save every 1 second
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastSaveTimeAGL >= 1000) {
//                        saveSpeedToFile(String.format("%.2f Knots", lastSaveTimeAGL));
                        appendLogEntry(feet);
                        lastSaveTimeAGL = currentTime; // Update last save timestamp
                    }
                } catch (NumberFormatException e) {
                    Log.e("data_collected", "Failed to parse numeric value: " + decodedString, e);
                }
            }
        }
    }

    void status(String str) {
        SpannableStringBuilder spn = new SpannableStringBuilder(str);
        spn.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.colorStatusText)), 0, spn.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        statusText.setText(spn);
        receiveText.setText("0,0 AGL");
    }

    class ControlLines {
        private static final int refreshInterval = 200; // msec

        private final Runnable runnable;
        private final ToggleButton rtsBtn, ctsBtn, dtrBtn, dsrBtn, cdBtn, riBtn;

        ControlLines(View view) {
            runnable = this::run; // w/o explicit Runnable, a new lambda would be created on each postDelayed, which would not be found again by removeCallbacks
            rtsBtn = null;
            ctsBtn = null;
            dtrBtn = null;
            dsrBtn = null;
            cdBtn = null;
            riBtn = null;
        }

        private void toggle(View v) {
            ToggleButton btn = (ToggleButton) v;
            if (!connected) {
                btn.setChecked(!btn.isChecked());
                Toast.makeText(getActivity(), "not connected", Toast.LENGTH_SHORT).show();
                return;
            }
            String ctrl = "";
            try {
                if (btn.equals(rtsBtn)) { ctrl = "RTS"; usbSerialPort.setRTS(btn.isChecked()); }
                if (btn.equals(dtrBtn)) { ctrl = "DTR"; usbSerialPort.setDTR(btn.isChecked()); }
            } catch (IOException e) {
                status("set" + ctrl + "() failed: " + e.getMessage());
            }
        }

        private void run() {
            if (!connected)
                return;
            try {
                EnumSet<UsbSerialPort.ControlLine> controlLines = usbSerialPort.getControlLines();
                rtsBtn.setChecked(controlLines.contains(UsbSerialPort.ControlLine.RTS));
                ctsBtn.setChecked(controlLines.contains(UsbSerialPort.ControlLine.CTS));
                dtrBtn.setChecked(controlLines.contains(UsbSerialPort.ControlLine.DTR));
                dsrBtn.setChecked(controlLines.contains(UsbSerialPort.ControlLine.DSR));
                cdBtn.setChecked(controlLines.contains(UsbSerialPort.ControlLine.CD));
                riBtn.setChecked(controlLines.contains(UsbSerialPort.ControlLine.RI));
                mainLooper.postDelayed(runnable, refreshInterval);
            } catch (Exception e) {
                status("getControlLines() failed: " + e.getMessage() + " -> stopped control line refresh");
            }
        }

        void start() {
            if (!connected)
                return;
            try {
                usbSerialPort.setDTR(true); // Force DTR to always be true
            } catch (Exception e) {
                Toast.makeText(getActivity(), "Failed to set DTR: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
        }

        void stop() {
            mainLooper.removeCallbacks(runnable);
            rtsBtn.setChecked(false);
            ctsBtn.setChecked(false);
            dtrBtn.setChecked(false);
            dsrBtn.setChecked(false);
            cdBtn.setChecked(false);
            riBtn.setChecked(false);
        }
    }
}
