<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <View
        android:layout_width="match_parent"
        android:background="?android:attr/listDivider"
        android:layout_height="2dp" />

    <TextView
        android:id="@+id/receive_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:gravity="center"
        android:textAppearance="@style/TextAppearance.AppCompat.Large"
        android:typeface="monospace"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:layout_margin="16dp"/>

<!--    <TextView-->
<!--        android:id="@+id/gpsStatus"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="GPS Status" />-->

    <TextView
        android:id="@+id/groundSpeed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ground Speed" />

    <TextView
        android:id="@+id/gpsStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="GPS"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/altStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ALT"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/batStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="BAT"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/pwrStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="PWR"
        android:textSize="18sp" />

<!--    <Button-->
<!--        android:id="@+id/refreshGpsButton"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Refresh" />-->

    <Button
        android:id="@+id/refreshGpsButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Refresh GPS" />

    <Button
        android:id="@+id/receive_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Read" />

    <View
        android:layout_width="match_parent"
        android:background="?android:attr/listDivider"
        android:layout_height="2dp" />

</LinearLayout>

