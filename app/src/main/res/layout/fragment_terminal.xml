<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <TextView-->
<!--        android:id="@+id/receive_text"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="TextView"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        android:textSize="70sp"-->
<!--        android:padding="5dp"-->
<!--        android:layout_marginTop="40dp"-->
<!--        android:layout_marginEnd="80dp" />-->

<com.drakkentech.flightlogger2.util.SquareRelativeLayout
        android:id="@+id/navscreenLeft"
        android:layout_width="350dp"
        android:layout_height="350dp"
        android:clipChildren="true"
        android:focusable="false"
        android:focusableInTouchMode="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="80dp"
        android:layout_marginStart="50dp">
    </com.drakkentech.flightlogger2.util.SquareRelativeLayout>

    <TextView
        android:id="@+id/status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Status"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp" />

<!--    <TextView-->
<!--        android:id="@+id/receive_text"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="TextView"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        android:textAppearance="@style/TextAppearance.AppCompat.Large"-->
<!--        android:padding="5dp"-->
<!--        android:layout_marginTop="40dp"-->
<!--        android:layout_marginEnd="250dp" />-->

    <TextView
        android:id="@+id/receive_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0,0 AGL"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="80sp"
        android:padding="5dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="100dp" />


    <TextView
        android:id="@+id/groundSpeed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0,0 Knots"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="80sp"
        android:padding="5dp"
        android:layout_marginTop="160dp"
        android:layout_marginEnd="100dp" />

    <TextView
        android:id="@+id/textViewthree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="5Y-AJP_2014-02-27.gpx\n001 ~ 036"
        android:textSize="20sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginEnd="300dp"
        android:padding="1dp"
        />
<!--        android:background="@drawable/text_border" -->

    <TextView
        android:id="@+id/textViewfour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="START TRANSECT \n"
        android:textSize="20sp"
        android:padding="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginEnd="80dp"
        />
<!--        android:background="@drawable/text_border" -->

<!--    <TextView-->
<!--        android:id="@+id/groundSpeed"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Ground Speed"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        android:textSize="18sp"-->
<!--        android:layout_marginTop="50dp"-->
<!--        android:layout_marginEnd="500dp" />-->

    <TextView
        android:id="@+id/gpsStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="GPS"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginStart="150dp" />

    <TextView
        android:id="@+id/altStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ALT"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginStart="110dp" />

    <TextView
        android:id="@+id/batStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="BAT"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginStart="70dp" />

    <TextView
        android:id="@+id/pwrStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="PWR"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginStart="20dp" />

<!--    <Button-->
<!--        android:id="@+id/refreshGpsButton"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Refresh GPS"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        android:layout_marginBottom="16dp"-->
<!--        android:layout_marginStart="400dp" />-->
    <Button
        android:id="@+id/refreshGpsButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Refresh GPS"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp" />

<!--    <Button-->
<!--        android:id="@+id/nav_header_file_button"-->
<!--        android:layout_width="44dp"-->
<!--        android:layout_height="56dp"-->
<!--        android:layout_marginLeft="28dp"-->
<!--        android:layout_marginTop="12dp"-->
<!--        android:layout_marginRight="12dp"-->
<!--        android:background="@drawable/filefolder"-->
<!--        android:clickable="false"-->
<!--        android:onClick="browseGpxFiles"-->
<!--        android:text=""-->
<!--        android:textSize="12sp" />-->
<!--    Will use onclick check example on top-->
    <Button
        android:id="@+id/nav_header_file_button"
        android:layout_width="44dp"
        android:layout_height="56dp"
        android:layout_marginLeft="28dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/filefolder"
        android:clickable="false"
        android:text=""
        android:textSize="12sp" />

    <Button
        android:id="@+id/receive_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Read"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="250dp"
        android:layout_marginEnd="500dp" />

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:background="?android:attr/listDivider"-->
<!--        android:layout_height="2dp"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        android:layout_marginTop="300dp"-->
<!--        android:layout_marginEnd="500dp" />-->

</androidx.constraintlayout.widget.ConstraintLayout>
