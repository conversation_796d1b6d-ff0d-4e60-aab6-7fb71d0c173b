plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.drakkentech.flightlogger2"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.drakkentech.flightlogger2"
        minSdk = 27
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {

    implementation ("com.github.mik3y:usb-serial-for-android:3.8.1")
    implementation ("androidx.appcompat:appcompat:1.6.1")
    implementation ("com.google.android.material:material:1.11.0")
    implementation ("androidx.constraintlayout:constraintlayout:2.1.0")
    implementation ("com.google.android.gms:play-services-location:18.0.0")
//    implementation(libs.appcompat)
    implementation(libs.material)
//    implementation(libs.constraintlayout)
//    implementation(libs.navigation.fragment)
//    implementation(libs.navigation.ui)
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
}